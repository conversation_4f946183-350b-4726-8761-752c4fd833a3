# 部署指南

## D1数据库设置

### 1. 创建D1数据库

```bash
# 创建D1数据库
npx wrangler d1 create mulink-logs
```

执行后会得到数据库ID，需要更新 `wrangler.jsonc` 中的 `database_id` 字段。

### 2. 更新配置文件

将生成的数据库ID替换 `wrangler.jsonc` 中的 `placeholder-will-be-generated`：

```jsonc
{
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "mulink-logs",
      "database_id": "your-actual-database-id-here"
    }
  ]
}
```

### 3. 初始化数据库表结构

```bash
# 本地开发环境
npx wrangler d1 execute mulink-logs --local --file=./schema.sql

# 生产环境
npx wrangler d1 execute mulink-logs --file=./schema.sql
```

### 4. 部署Worker

```bash
npm run deploy
```

## API接口

### 请求日志列表
```
GET /api/logs?page=1&limit=20&status=approved&slug=example
```

参数：
- `page`: 页码，默认1
- `limit`: 每页数量，默认20，最大100
- `status`: 可选，过滤状态 (approved/reject/failure)
- `slug`: 可选，过滤特定短链接

### 请求详情
```
GET /api/logs/{requestId}
```

返回指定requestId的详细信息，包括完整的raw数据。

## 数据库表结构

`request_logs` 表包含以下字段：
- `request_id`: 唯一请求ID
- `slug`: 短链接标识
- `target`: 目标URL
- `ip`: IP地址
- `status_text`: 请求状态 (approved/reject/failure)
- `location`: 二字国家代码
- `duration_ms`: 响应时间(毫秒)
- `timestamp`: Unix时间戳
- `raw`: 详细信息JSON格式存储
- `created_at`: 创建时间

### raw字段包含的详细信息：
- `url`: 完整的请求URL
- `search`: URL查询参数部分
- `method`: HTTP方法 (GET/POST等)
- `headers`: 完整请求头对象
- `cf`: Cloudflare相关信息对象（通过c.req.raw.cf获取）
  - `asn`: ASN编号
  - `asOrganization`: ASN组织
  - `colo`: 数据中心代码
  - `country`: 国家代码
  - `city`: 城市
  - `continent`: 大洲
  - `latitude`: 纬度
  - `longitude`: 经度
  - `postalCode`: 邮政编码
  - `metroCode`: 都市区代码
  - `region`: 地区
  - `regionCode`: 地区代码
  - `timezone`: 时区
  - `httpProtocol`: HTTP协议版本
  - `tlsVersion`: TLS版本
  - `tlsCipher`: TLS加密套件
  - `ray`: Cloudflare Ray ID
  - `connectingIp`: 连接IP
  - `visitor`: 访问者信息

## 注意事项

1. 日志记录是异步的，不会影响短链接跳转性能
2. 只记录非 `/api/*` 路径的请求
3. 数据库查询已优化，包含必要的索引
4. 错误处理确保日志记录失败不会影响主要功能
