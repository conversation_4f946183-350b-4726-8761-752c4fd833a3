-- 创建请求日志表
CREATE TABLE IF NOT EXISTS request_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  request_id TEXT UNIQUE NOT NULL,
  slug TEXT NOT NULL,
  status TEXT NOT NULL, -- 'success', 'not_found', 'error'
  target_url TEXT,
  
  -- 请求信息
  method TEXT NOT NULL DEFAULT 'GET',
  user_agent TEXT,
  ip_address TEXT,
  referer TEXT,
  accept_language TEXT,
  
  -- 请求头信息 (JSON格式存储)
  headers TEXT, -- JSON string
  
  -- 响应信息
  response_time INTEGER, -- 响应时间(毫秒)
  status_code INTEGER,
  error_message TEXT,
  
  -- 时间戳
  timestamp INTEGER NOT NULL, -- Unix timestamp
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  -- 额外信息
  country TEXT,
  city TEXT,
  cf_ray TEXT
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp ON request_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_request_logs_slug ON request_logs(slug);
CREATE INDEX IF NOT EXISTS idx_request_logs_status ON request_logs(status);
CREATE INDEX IF NOT EXISTS idx_request_logs_created_at ON request_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_request_logs_request_id ON request_logs(request_id);
