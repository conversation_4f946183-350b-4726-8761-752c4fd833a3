-- 创建请求日志表
CREATE TABLE IF NOT EXISTS request_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  request_id TEXT UNIQUE NOT NULL,
  slug TEXT NOT NULL,
  target TEXT, -- 目标URL
  ip TEXT, -- IP地址
  status_text TEXT NOT NULL, -- 'approved', 'reject', 'failure'
  location TEXT, -- 二字国家代码
  duration_ms INTEGER, -- 响应时间(毫秒)
  raw TEXT, -- 详细信息JSON格式存储
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_request_logs_created_at ON request_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_request_logs_slug ON request_logs(slug);
CREATE INDEX IF NOT EXISTS idx_request_logs_status_text ON request_logs(status_text);
CREATE INDEX IF NOT EXISTS idx_request_logs_request_id ON request_logs(request_id);
