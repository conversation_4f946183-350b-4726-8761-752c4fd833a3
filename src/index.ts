import { Hono } from "hono";
import { cors } from "hono/cors";
import { basicAuth } from "hono/basic-auth";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

// 请求日志记录接口
interface RequestLogData {
  requestId: string;
  slug: string;
  target?: string;
  ip?: string;
  statusText: 'approved' | 'reject' | 'failure';
  location?: string; // 二字国家代码
  durationMs?: number;
  raw: string; // 详细信息JSON格式
}



const app = new Hono<{ Bindings: CloudflareBindings }>();

app.use(
  "/api/*",
  cors({
    origin: (origin) => {
      return origin.endsWith(".melook.app") ||
        origin.includes("localhost") ||
        origin.includes("vusercontent.net")
        ? origin
        : "https://google.com";
    },
    allowMethods: ["GET", "HEAD", "POST", "PUT", "DELETE"],
  })
);

app.use("/api/*", basicAuth({
  username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  password: "TJNIUBI666"
}));

// 创建短链接
app.post(
  "/api/link",
  zValidator(
    "json",
    z.object({
      slug: z.string(),
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const { slug, targets } = c.req.valid("json");

    // 检查slug是否已存在
    const existingTargets = await c.env.KV.get(slug);
    if (existingTargets) {
      return c.json({ error: "短链接已存在，不能覆盖" }, 409);
    }

    await c.env.KV.put(slug, JSON.stringify(targets));
    return c.text("OK");
  }
);

// 获取短链接信息
app.get("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }
  return c.json(JSON.parse(targets));
});

// 删除短链接
app.delete("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  await c.env.KV.delete(slug);
  return c.text("OK");
});

// 获取请求日志列表
app.get("/api/logs", async (c) => {
  try {
    const page = parseInt(c.req.query("page") || "1");
    const limit = Math.min(parseInt(c.req.query("limit") || "20"), 100); // 最大100条
    const statusText = c.req.query("status"); // 可选的状态过滤 (approved/reject/failure)
    const slug = c.req.query("slug"); // 可选的slug过滤

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = "1=1";
    const params: any[] = [];

    if (statusText) {
      whereClause += " AND status_text = ?";
      params.push(statusText);
    }

    if (slug) {
      whereClause += " AND slug = ?";
      params.push(slug);
    }

    // 查询总数
    const countStmt = c.env.DB.prepare(`
      SELECT COUNT(*) as total FROM request_logs WHERE ${whereClause}
    `);
    const countResult = await countStmt.bind(...params).first();
    const total = Number(countResult?.total || 0);

    // 查询数据
    const stmt = c.env.DB.prepare(`
      SELECT
        request_id, slug, target, ip, status_text, location,
        duration_ms, created_at
      FROM request_logs
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `);

    const results = await stmt.bind(...params, limit, offset).all();

    return c.json({
      data: results.results || [],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Failed to fetch logs:', error);
    return c.json({ error: "Failed to fetch logs" }, 500);
  }
});

// 获取单个请求详情
app.get("/api/logs/:requestId", async (c) => {
  try {
    const requestId = c.req.param("requestId");

    const stmt = c.env.DB.prepare(`
      SELECT
        request_id, slug, target, ip, status_text, location,
        duration_ms, raw, created_at
      FROM request_logs
      WHERE request_id = ?
    `);

    const result = await stmt.bind(requestId).first();

    if (!result) {
      return c.json({ error: "Request not found" }, 404);
    }

    // 解析raw JSON字符串
    let parsedRaw = {};
    try {
      if (result.raw) {
        parsedRaw = JSON.parse(result.raw as string);
      }
    } catch (e) {
      console.error('Failed to parse raw data:', e);
    }

    return c.json({
      requestId: result.request_id,
      slug: result.slug,
      target: result.target,
      ip: result.ip,
      statusText: result.status_text,
      location: result.location,
      durationMs: result.duration_ms,
      createdAt: result.created_at,
      raw: parsedRaw
    });
  } catch (error) {
    console.error('Failed to fetch log detail:', error);
    return c.json({ error: "Failed to fetch log detail" }, 500);
  }
});

// 主页
app.get("/", async (c) => {
  return c.text("MuLink - 短链接服务");
});

// 通配符路由处理所有slug访问
app.get("/*", async (c) => {
  const url = new URL(c.req.url);
  const slug = url.pathname.slice(1);

  // 跳过空路径和已知路径
  if (!slug || slug.startsWith("api/") || slug.startsWith(".")) {
    return c.text("Not found", 404);
  }

  return handleSlugRedirect(c, slug);
});

function weightPick(targets: { url: string; weight: number }[]) {
  const totalWeight = targets.reduce((acc, cur) => acc + cur.weight, 0);
  const random = Math.random() * totalWeight;
  let currentWeight = 0;
  for (const target of targets) {
    currentWeight += target.weight;
    if (random <= currentWeight) {
      return target.url;
    }
  }
  return targets[0].url;
}

function mergeSearchParams(originalUrl: string, targetUrl: string) {
  const original = new URL(originalUrl);
  const target = new URL(targetUrl);

  for (const [key, value] of original.searchParams) {
    target.searchParams.set(key, value);
  }

  return target.toString();
}

// 生成唯一的请求ID
function generateRequestId(): string {
  return crypto.randomUUID();
}

// 收集请求头信息
function collectHeaders(request: Request): Record<string, string> {
  const headers: Record<string, string> = {};

  // 收集常用的请求头
  const headersToCollect = [
    'user-agent',
    'referer',
    'accept',
    'accept-language',
    'accept-encoding',
    'x-forwarded-for',
    'cf-ray',
    'cf-ipcountry',
    'cf-connecting-ip',
    'x-real-ip'
  ];

  headersToCollect.forEach(headerName => {
    const value = request.headers.get(headerName);
    if (value) {
      headers[headerName] = value;
    }
  });

  return headers;
}

// 记录请求到D1数据库
async function logRequest(c: any, logData: RequestLogData): Promise<void> {
  try {
    const stmt = c.env.DB.prepare(`
      INSERT INTO request_logs (
        request_id, slug, target, ip, status_text, location, duration_ms, raw
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    await stmt.bind(
      logData.requestId,
      logData.slug,
      logData.target,
      logData.ip,
      logData.statusText,
      logData.location,
      logData.durationMs,
      logData.raw
    ).run();
  } catch (error) {
    // 记录日志失败不应该影响主要功能
    console.error('Failed to log request:', error);
  }
}

async function handleSlugRedirect(c: any, slug: string) {
  const startTime = Date.now();
  const requestId = generateRequestId();

  // 收集请求信息
  const headers = collectHeaders(c.req);
  const userAgent = c.req.header('user-agent');
  const ipAddress = c.req.header('cf-connecting-ip') || c.req.header('x-forwarded-for') || c.req.header('x-real-ip');
  const referer = c.req.header('referer');
  const acceptLanguage = c.req.header('accept-language');
  const country = c.req.header('cf-ipcountry');
  const cfRay = c.req.header('cf-ray');

  // 构建raw数据对象
  const rawData = {
    method: c.req.method,
    userAgent,
    referer,
    acceptLanguage,
    headers,
    timestamp: Math.floor(Date.now() / 1000),
    cfRay,
    url: c.req.url
  };

  try {
    const targets = await c.env.KV.get(slug);
    if (!targets) {
      // 记录未找到的请求
      const logData: RequestLogData = {
        requestId,
        slug,
        ip: ipAddress,
        statusText: 'reject',
        location: country,
        durationMs: Date.now() - startTime,
        raw: JSON.stringify({
          ...rawData,
          statusCode: 404,
          errorMessage: 'Slug not found'
        })
      };

      // 异步记录日志，不阻塞响应
      c.executionCtx.waitUntil(logRequest(c, logData));

      return c.text("Not found", 404);
    }

    const targetsJson = JSON.parse(targets) as { url: string; weight: number }[];
    const pickedUrl = weightPick(targetsJson);
    const finalUrl = mergeSearchParams(c.req.url, pickedUrl);

    // 记录成功的请求
    const logData: RequestLogData = {
      requestId,
      slug,
      target: finalUrl,
      ip: ipAddress,
      statusText: 'approved',
      location: country,
      durationMs: Date.now() - startTime,
      raw: JSON.stringify({
        ...rawData,
        statusCode: 302,
        targetUrl: finalUrl,
        pickedUrl,
        availableTargets: targetsJson.length
      })
    };

    // 异步记录日志，不阻塞响应
    c.executionCtx.waitUntil(logRequest(c, logData));

    // 返回HTTP重定向
    return c.redirect(finalUrl, 302);
  } catch (error) {
    // 记录错误的请求
    const logData: RequestLogData = {
      requestId,
      slug,
      ip: ipAddress,
      statusText: 'failure',
      location: country,
      durationMs: Date.now() - startTime,
      raw: JSON.stringify({
        ...rawData,
        statusCode: 500,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      })
    };

    // 异步记录日志，不阻塞响应
    c.executionCtx.waitUntil(logRequest(c, logData));

    return c.text("Internal Server Error", 500);
  }
}

export default app;
