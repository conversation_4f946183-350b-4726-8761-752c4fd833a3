import { Hono } from "hono";
import { cors } from "hono/cors";
import { basicAuth } from "hono/basic-auth";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

// 请求日志记录接口
interface RequestLogData {
  requestId: string;
  slug: string;
  target?: string;
  ip?: string;
  statusText: 'approved' | 'reject' | 'failure';
  location?: string; // 二字国家代码
  durationMs?: number;
  timestamp: number; // Unix时间戳
  raw: string; // 详细信息JSON格式: url, search, method, headers, cf
}



const app = new Hono<{ Bindings: CloudflareBindings }>();

app.use(
  "/api/*",
  cors({
    origin: (origin) => {
      return origin.endsWith(".melook.app") ||
        origin.includes("localhost") ||
        origin.includes("vusercontent.net")
        ? origin
        : "https://google.com";
    },
    allowMethods: ["GET", "HEAD", "POST", "PUT", "DELETE"],
  })
);

app.use("/api/*", basicAuth({
  username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  password: "TJNIUBI666"
}));

// 创建短链接
app.post(
  "/api/link",
  zValidator(
    "json",
    z.object({
      slug: z.string(),
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const { slug, targets } = c.req.valid("json");

    // 检查slug是否已存在
    const existingTargets = await c.env.KV.get(slug);
    if (existingTargets) {
      return c.json({ error: "短链接已存在，不能覆盖" }, 409);
    }

    await c.env.KV.put(slug, JSON.stringify(targets));
    return c.text("OK");
  }
);

// 获取短链接信息
app.get("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }
  return c.json(JSON.parse(targets));
});

// 删除短链接
app.delete("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  await c.env.KV.delete(slug);
  return c.text("OK");
});

// 获取请求日志列表
app.get("/api/logs", async (c) => {
  try {
    // 检查数据库是否可用
    if (!c.env.DB) {
      return c.json({ error: "Database not available" }, 503);
    }

    const page = parseInt(c.req.query("page") || "1");
    const limit = Math.min(parseInt(c.req.query("limit") || "20"), 100); // 最大100条
    const statusText = c.req.query("status"); // 可选的状态过滤 (approved/reject/failure)
    const slug = c.req.query("slug"); // 可选的slug过滤

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = "1=1";
    const params: any[] = [];

    if (statusText) {
      whereClause += " AND status_text = ?";
      params.push(statusText);
    }

    if (slug) {
      whereClause += " AND slug = ?";
      params.push(slug);
    }

    // 查询总数
    const countStmt = c.env.DB.prepare(`
      SELECT COUNT(*) as total FROM request_logs WHERE ${whereClause}
    `);
    const countResult = await countStmt.bind(...params).first();
    const total = Number(countResult?.total || 0);

    // 查询数据
    const stmt = c.env.DB.prepare(`
      SELECT
        request_id, slug, target, ip, status_text, location,
        duration_ms, timestamp, created_at
      FROM request_logs
      WHERE ${whereClause}
      ORDER BY timestamp DESC
      LIMIT ? OFFSET ?
    `);

    const results = await stmt.bind(...params, limit, offset).all();

    return c.json({
      data: results.results || [],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Failed to fetch logs:', error);
    return c.json({ error: "Failed to fetch logs" }, 500);
  }
});

// 获取单个请求详情
app.get("/api/logs/:requestId", async (c) => {
  try {
    // 检查数据库是否可用
    if (!c.env.DB) {
      return c.json({ error: "Database not available" }, 503);
    }

    const requestId = c.req.param("requestId");

    const stmt = c.env.DB.prepare(`
      SELECT
        request_id, slug, target, ip, status_text, location,
        duration_ms, timestamp, raw, created_at
      FROM request_logs
      WHERE request_id = ?
    `);

    const result = await stmt.bind(requestId).first();

    if (!result) {
      return c.json({ error: "Request not found" }, 404);
    }

    // 解析raw JSON字符串
    let parsedRaw = {};
    try {
      if (result.raw) {
        parsedRaw = JSON.parse(result.raw as string);
      }
    } catch (e) {
      console.error('Failed to parse raw data:', e);
    }

    return c.json({
      requestId: result.request_id,
      slug: result.slug,
      target: result.target,
      ip: result.ip,
      statusText: result.status_text,
      location: result.location,
      durationMs: result.duration_ms,
      timestamp: result.timestamp,
      createdAt: result.created_at,
      raw: parsedRaw
    });
  } catch (error) {
    console.error('Failed to fetch log detail:', error);
    return c.json({ error: "Failed to fetch log detail" }, 500);
  }
});

// 主页
app.get("/", async (c) => {
  return c.text("MuLink - 短链接服务");
});

// 通配符路由处理所有slug访问
app.get("/*", async (c) => {
  const url = new URL(c.req.url);
  const slug = url.pathname.slice(1);

  // 跳过空路径和已知路径
  if (!slug || slug.startsWith("api/") || slug.startsWith(".")) {
    return c.text("Not found", 404);
  }

  return handleSlugRedirect(c, slug);
});

function weightPick(targets: { url: string; weight: number }[]) {
  const totalWeight = targets.reduce((acc, cur) => acc + cur.weight, 0);
  const random = Math.random() * totalWeight;
  let currentWeight = 0;
  for (const target of targets) {
    currentWeight += target.weight;
    if (random <= currentWeight) {
      return target.url;
    }
  }
  return targets[0].url;
}

function mergeSearchParams(originalUrl: string, targetUrl: string) {
  const original = new URL(originalUrl);
  const target = new URL(targetUrl);

  for (const [key, value] of original.searchParams) {
    target.searchParams.set(key, value);
  }

  return target.toString();
}

// 生成唯一的请求ID
function generateRequestId(): string {
  return crypto.randomUUID();
}

// 收集所有请求头信息
function collectHeaders(request: Request): Record<string, string> {
  const headers: Record<string, string> = {};

  // 遍历所有请求头
  request.headers.forEach((value, key) => {
    headers[key] = value;
  });

  return headers;
}

// 记录请求到D1数据库
async function logRequest(c: any, logData: RequestLogData): Promise<void> {
  try {
    // 检查数据库是否可用
    if (!c.env.DB) {
      console.warn('Database not available, skipping log');
      return;
    }

    const stmt = c.env.DB.prepare(`
      INSERT INTO request_logs (
        request_id, slug, target, ip, status_text, location, duration_ms, timestamp, raw
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    await stmt.bind(
      logData.requestId,
      logData.slug,
      logData.target,
      logData.ip,
      logData.statusText,
      logData.location,
      logData.durationMs,
      logData.timestamp,
      logData.raw
    ).run();
  } catch (error) {
    // 记录日志失败不应该影响主要功能
    console.error('Failed to log request:', error);
  }
}

// 1. 请求解析 - 解析请求的基本信息
interface ParsedRequest {
  headers: Record<string, string>;
  ipAddress: string | undefined;
  country: string | undefined;
  cfData: any;
  url: string;
  search: string;
  method: string;
}

function parseRequest(c: any): ParsedRequest {
  const headers = collectHeaders(c.req);
  const ipAddress = c.req.raw.cf?.connectingIP || c.req.header('cf-connecting-ip');
  const country = c.req.raw.cf?.country;
  const cfData = c.req.raw.cf || {};
  const url = new URL(c.req.url);

  return {
    headers,
    ipAddress,
    country,
    cfData,
    url: c.req.url,
    search: url.search,
    method: c.req.method
  };
}

// 2. 目标查找 - 从KV获取目标URL列表
async function findTargets(c: any, slug: string): Promise<{ url: string; weight: number }[] | null> {
  try {
    const targets = await c.env.KV.get(slug);
    if (!targets) {
      return null;
    }
    return JSON.parse(targets) as { url: string; weight: number }[];
  } catch (error) {
    console.error('Failed to fetch targets from KV:', error);
    return null;
  }
}

// 3. URL选择和参数合并 - 选择目标URL并合并参数
function selectAndMergeUrl(originalUrl: string, targets: { url: string; weight: number }[]): string {
  const pickedUrl = weightPick(targets);
  return mergeSearchParams(originalUrl, pickedUrl);
}

// 4. 流量审核 - 基础的流量验证（可扩展）
function auditTraffic(parsedRequest: ParsedRequest, slug: string): { allowed: boolean; reason?: string } {
  // 这里可以添加各种审核逻辑，比如：
  // - IP黑名单检查
  // - 地理位置限制
  // - 用户代理检查
  // - 频率限制等

  // 目前简单返回允许
  return { allowed: true };
}

// 5. 日志记录 - 异步记录请求日志
async function recordRequestLog(
  c: any,
  slug: string,
  parsedRequest: ParsedRequest,
  result: {
    status: 'approved' | 'reject' | 'failure';
    target?: string;
    startTime: number;
    errorMessage?: string;
  }
): Promise<void> {
  try {
    const requestId = generateRequestId();
    const timestamp = Math.floor(Date.now() / 1000);

    // 构建raw数据对象
    const rawData = {
      url: parsedRequest.url,
      search: parsedRequest.search,
      method: parsedRequest.method,
      headers: parsedRequest.headers,
      cf: parsedRequest.cfData
    };

    const logData: RequestLogData = {
      requestId,
      slug,
      target: result.target,
      ip: parsedRequest.ipAddress,
      statusText: result.status,
      location: parsedRequest.country,
      durationMs: Date.now() - result.startTime,
      timestamp,
      raw: JSON.stringify(rawData)
    };

    // 异步记录日志，不阻塞响应
    if (c.executionCtx && c.executionCtx.waitUntil) {
      c.executionCtx.waitUntil(logRequest(c, logData));
    }
  } catch (error) {
    // 日志记录失败不影响主要功能
    console.error('Failed to record request log:', error);
  }
}

// 6. 主处理函数 - 协调各个原子化功能
async function handleSlugRedirect(c: any, slug: string) {
  const startTime = Date.now();

  try {
    // 1. 解析请求
    const parsedRequest = parseRequest(c);

    // 2. 审核流量
    const auditResult = auditTraffic(parsedRequest, slug);
    if (!auditResult.allowed) {
      // 记录被拒绝的请求
      await recordRequestLog(c, slug, parsedRequest, {
        status: 'reject',
        startTime,
        errorMessage: auditResult.reason || 'Traffic audit failed'
      });
      return c.text("Access Denied", 403);
    }

    // 3. 查找目标
    const targets = await findTargets(c, slug);
    if (!targets) {
      // 记录未找到的请求
      await recordRequestLog(c, slug, parsedRequest, {
        status: 'reject',
        startTime,
        errorMessage: 'Slug not found'
      });
      return c.text("Not found", 404);
    }

    // 4. 选择URL并合并参数
    const finalUrl = selectAndMergeUrl(parsedRequest.url, targets);

    // 5. 记录成功的请求
    await recordRequestLog(c, slug, parsedRequest, {
      status: 'approved',
      target: finalUrl,
      startTime
    });

    // 6. 返回重定向
    return c.redirect(finalUrl, 302);

  } catch (error) {
    console.error('Error in handleSlugRedirect:', error);

    // 记录错误的请求
    try {
      const parsedRequest = parseRequest(c);
      await recordRequestLog(c, slug, parsedRequest, {
        status: 'failure',
        startTime,
        errorMessage: error instanceof Error ? error.message : String(error)
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return c.text("Internal Server Error", 500);
  }
}

export default app;
